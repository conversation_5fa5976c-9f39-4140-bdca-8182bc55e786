'use strict';

// Node built-in modules
import crypto from 'node:crypto';
import readline from 'node:readline';

// External dependencies
import { PrintOrderTask_status, Prisma, PrismaClient } from '@prisma/client';
import pino from 'pino';
import { z } from 'zod';

// Internal imports
import { OrderWithItemsAndProducts } from '../../lib/order-processing';
import { fetchOrdersWithRetry, updateOrderItemsOptionsBatch } from '../../lib/shipstation/api.js';
import {
  AiOrderResponseSchema,
  PersonalizationDetailSchema,
  type AiOrderItemData,
  type OrderDebugInfo,
  type ProcessingOptions,
  type ShipStationSyncResult,
  type TaskCreationResult,
  type TaskPersonalizationData,
} from '../populate-print-queue.types.js';

// Helper function for confirmation prompts
async function confirmExecution(promptMessage: string): Promise<boolean> {
  const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
  return new Promise(resolve => {
    rl.question(`${promptMessage} (yes/NO) `, answer => {
      rl.close();
      resolve(answer.toLowerCase() === 'yes');
    });
  });
}

// Helper function for building personalized details string
function buildPersonalizedDetailsString(
  personalizations: Array<z.infer<typeof PersonalizationDetailSchema>>,
  lineItemKeyForLog: string,
  orderIdForLog: number,
  logger: pino.Logger
): string {
  if (!personalizations || personalizations.length === 0) {
    logger.info(
      `[Util][Order ${orderIdForLog}][Item ${lineItemKeyForLog}] No personalizations to build details string from.`
    );
    return 'No personalization details extracted.';
  }
  const personalizedDetailStrings: string[] = [];
  for (const p of personalizations) {
    const text = p.customText || 'N/A';
    const color1 = p.color1;
    const color2 = p.color2 ? ` / ${p.color2}` : '';
    personalizedDetailStrings.push(`${text} (${color1 || 'N/A'}${color2})`);
  }
  let combinedDetailsString = personalizedDetailStrings.join(', ');
  const MAX_LEN_SHIPSTATION_OPTION = 200;
  const TRUNCATION_SUFFIX = '... (See Packing List)';
  if (combinedDetailsString.length > MAX_LEN_SHIPSTATION_OPTION) {
    combinedDetailsString =
      combinedDetailsString.substring(0, MAX_LEN_SHIPSTATION_OPTION - TRUNCATION_SUFFIX.length) +
      TRUNCATION_SUFFIX;
  }
  logger.info(
    `[Util][Order ${orderIdForLog}][Item ${lineItemKeyForLog}] Built personalized details string: "${combinedDetailsString}"`
  );
  return combinedDetailsString;
}

// Helper function for parsing bulk personalization notes
function parseBulkPersonalizationNotes(
  notes: string
): Array<{ customText: string; color1: string; color2: string }> {
  if (!notes) return [];
  const lines = notes.split(/\r?\n/);
  const result = [];
  let currentColor1: string | null = null;
  let currentColor2: string | null = null;
  const colorHeaderRegex = /Primary ([^&\n]+)\s*&\s*Secondary ([^\n]+)/i;
  for (const line of lines) {
    const trimmed = line.trim();
    if (!trimmed) continue;
    const match = trimmed.match(colorHeaderRegex);
    if (match) {
      currentColor1 = match[1].replace(/^[^a-zA-Z]*/, '').trim();
      currentColor2 = match[2].replace(/^[^a-zA-Z]*/, '').trim();
      continue;
    }
    // If line is not a color header, treat as name if color context exists
    if (currentColor1 && currentColor2 && /^[a-zA-Z\-\s']+$/.test(trimmed)) {
      result.push({ customText: trimmed, color1: currentColor1, color2: currentColor2 });
    }
  }
  return result;
}

/**
 * Creates or updates print order tasks in a database transaction
 */
export async function createOrUpdateTasksInTransaction(
  tx: Prisma.TransactionClient,
  order: OrderWithItemsAndProducts,
  aiData: z.infer<typeof AiOrderResponseSchema> | undefined,
  options: ProcessingOptions,
  orderDebugInfo: OrderDebugInfo,
  originalItemsForPrompt: AiOrderItemData[],
  liveCustomerNotes: string | null | undefined,
  liveInternalNotes: string | null | undefined,
  logger: pino.Logger
): Promise<TaskCreationResult> {
  let tasksCreatedCount = 0;
  let tasksSkippedCount = 0;
  let itemsNeedReviewCount = 0;

  // Create a map for easy lookup of original item data including _amazon flags
  const originalItemsMap = new Map<string, AiOrderItemData>();
  originalItemsForPrompt.forEach(item => {
    if (item.id) {
      // id is shipstationLineItemKey
      originalItemsMap.set(item.id, item);
    }
  });

  const itemsToPatch: Record<string, Array<{ name: string; value: string | null }>> = {};
  const patchReasons: string[] = [];

  for (const item of order.OrderItem) {
    const orderItemId = item.id;
    logger.info(
      {
        orderId: order.id,
        itemId: orderItemId,
        shipstationLineItemKey: item.shipstationLineItemKey,
      },
      `[createOrUpdateTasksInTransaction] Processing item in loop.`
    );
    const preservedTexts = new Map<number, string | null>();
    const product = item.Product; // Get product from the item

    // Populate preservedTexts if options.preserveText is true, regardless of forceRecreate
    if (options.preserveText && !options.dryRun) {
      const existingTasks = await tx.printOrderTask.findMany({
        where: { orderItemId: orderItemId },
        select: { taskIndex: true, custom_text: true },
        orderBy: { taskIndex: 'asc' },
      });
      existingTasks.forEach(task => preservedTexts.set(task.taskIndex, task.custom_text));
      if (existingTasks.length > 0) {
        logger.info(
          `[DB][Order ${order.id}][Item ${orderItemId}] Preserved text for ${existingTasks.length} tasks due to --preserve-text.`
        );
      }
    } else if (options.preserveText && options.dryRun) {
      logger.info(
        `[Dry Run][Order ${order.id}][Item ${orderItemId}] Would fetch and preserve text due to --preserve-text.`
      );
    }

    // --- Preserve Text Logic (Placeholder for now) ---
    if (options.forceRecreate && !options.dryRun) {
      // Fetch existing tasks if preserveText is also true, before deleting
      // This block is now redundant for fetching, as it's done above if options.preserveText is true.
      // We only need to handle deletion here.
      logger.info(
        `[DB-DEBUG][Order ${order.id}][Item ${orderItemId}] PRE-DELETE: Attempting to delete tasks where orderItemId = ${orderItemId} due to --force-recreate.`
      );
      const { count } = await tx.printOrderTask.deleteMany({
        where: { orderItemId: orderItemId },
      });
      logger.info(
        `[DB-DEBUG][Order ${order.id}][Item ${orderItemId}] POST-DELETE: Deleted ${count} tasks for orderItemId = ${orderItemId}.`
      );
    } else if (options.forceRecreate && options.dryRun) {
      logger.info(
        `[Dry Run][Order ${order.id}][Item ${orderItemId}] Would delete existing tasks due to forceRecreate.`
      );
      if (options.preserveText) {
        logger.info(
          `[Dry Run][Order ${order.id}][Item ${orderItemId}] Would fetch and preserve text before deletion.`
        );
        // In a real scenario, you might simulate fetching to see if tasks exist
      }
    }

    let itemDebugEntry = orderDebugInfo.items.find(i => i.itemId === item.id);
    if (!itemDebugEntry) {
      itemDebugEntry = { itemId: item.id, status: 'Pending Transaction Logic', createdTaskIds: [] };
      orderDebugInfo.items.push(itemDebugEntry);
    } else {
      itemDebugEntry.status = 'Starting Transaction Logic';
      itemDebugEntry.createdTaskIds = [];
    }

    const taskDetailsToCreate: TaskPersonalizationData[] = [];
    let finalDataSource: string | null = null; // Will be 'AmazonURL', 'AI_Direct', 'Placeholder', etc.
    const lineItemKey = item.shipstationLineItemKey;

    // --- Try Direct Extraction First (e.g., Amazon URL) ---
    // For now, we'll assume direct extraction is handled elsewhere and focus on AI/Placeholder logic
    finalDataSource = 'AI'; // Default assumption if not AmazonURL
    const itemPzResult = lineItemKey ? aiData?.itemPersonalizations[lineItemKey] : undefined;

    // Improved fallback: If AI returns only a single empty/invalid personalization, treat as failure
    let useNotesParser = false;
    if (options.skipAi) {
      logger.info(
        `[DB][Order ${order.id}][Item ${orderItemId}] AI processing skipped via --skip-ai. Will create placeholder if enabled.`
      );
      finalDataSource = 'Skipped_AI';
    } else if (
      itemPzResult &&
      itemPzResult.personalizations &&
      itemPzResult.personalizations.length > 0
    ) {
      // Check if it's a single empty/invalid personalization
      if (
        itemPzResult.personalizations.length === 1 &&
        ((!itemPzResult.personalizations[0].customText &&
          !itemPzResult.personalizations[0].color1 &&
          !itemPzResult.personalizations[0].color2) ||
          itemPzResult.personalizations[0].needsReview === true)
      ) {
        logger.warn(
          { orderId: order.id, itemId: orderItemId, lineItemKey },
          'AI returned only a single empty/invalid personalization. Will trigger notes parser fallback.'
        );
        useNotesParser = true;
      } else {
        itemPzResult.personalizations.forEach(
          (p: z.infer<typeof PersonalizationDetailSchema>, _taskIndex: number) => {
            const taskDataForArray: TaskPersonalizationData = {
              custom_text: p.customText,
              color_1: p.color1,
              color_2: p.color2,
              quantity: p.quantity || 1,
              needs_review: p.needsReview || itemPzResult.overallNeedsReview || false,
              review_reason: p.reviewReason || itemPzResult.overallReviewReason,
              status: PrintOrderTask_status.pending,
              annotation: p.annotation,
            };
            taskDetailsToCreate.push(taskDataForArray);
          }
        );
        finalDataSource = 'AI_Direct';
        if (itemDebugEntry) itemDebugEntry.status = 'AI Data Parsed';
      }
    } else {
      useNotesParser = true;
    }

    if (useNotesParser) {
      logger.warn(
        { orderId: order.id, itemId: orderItemId, lineItemKey },
        'No valid AI personalizations, using notes parser fallback.'
      );
      // --- Use live notes for fallback parser if available ---
      const liveCustomerNotesStr =
        liveCustomerNotes !== null && liveCustomerNotes !== undefined
          ? String(liveCustomerNotes).trim()
          : '';
      const liveInternalNotesStr =
        liveInternalNotes !== null && liveInternalNotes !== undefined
          ? String(liveInternalNotes).trim()
          : '';
      const dbCustomerNotes = order.customer_notes ? order.customer_notes.trim() : '';
      const dbInternalNotes = order.internal_notes ? order.internal_notes.trim() : '';
      const legacyNotes = order.notes ? order.notes.trim() : '';
      const customerProfileNotes = order.Customer?.customer_notes
        ? order.Customer.customer_notes.trim()
        : '';

      // Use the first non-empty notes source
      let notes = '';
      let notesSource = 'none';

      if (liveCustomerNotesStr) {
        notes = liveCustomerNotesStr;
        notesSource = 'liveCustomerNotes';
      } else if (liveInternalNotesStr) {
        notes = liveInternalNotesStr;
        notesSource = 'liveInternalNotes';
      } else if (dbCustomerNotes) {
        notes = dbCustomerNotes;
        notesSource = 'dbCustomerNotes';
      } else if (dbInternalNotes) {
        notes = dbInternalNotes;
        notesSource = 'dbInternalNotes';
      } else if (legacyNotes) {
        notes = legacyNotes;
        notesSource = 'legacyNotes';
      } else if (customerProfileNotes) {
        notes = customerProfileNotes;
        notesSource = 'customerProfileNotes';
      }

      const parsedPersonalizations = parseBulkPersonalizationNotes(notes);
      if (parsedPersonalizations.length > 0) {
        parsedPersonalizations.forEach(p => {
          taskDetailsToCreate.push({
            custom_text: p.customText,
            color_1: p.color1,
            color_2: p.color2,
            quantity: 1,
            needs_review: false,
            review_reason: null,
            status: PrintOrderTask_status.pending,
            annotation: 'Parsed from customer notes',
          });
        });
        finalDataSource = 'NotesParser';
        if (itemDebugEntry) itemDebugEntry.status = 'NotesParser';
      } else if (options.createPlaceholder) {
        logger.info(
          { orderId: order.id, itemId: orderItemId },
          "Creating placeholder task as 'createPlaceholder' is true."
        );
        let placeholderReason = `No AI personalizations for lineItemKey ${lineItemKey}. Check order notes/options.`;
        taskDetailsToCreate.push({
          custom_text: 'Placeholder - Check Order Details',
          color_1: null,
          color_2: null,
          quantity: item.quantity || 1,
          needs_review: true,
          review_reason: placeholderReason.substring(0, 1000),
          status: PrintOrderTask_status.pending,
        });
        finalDataSource = 'Placeholder';
        if (itemDebugEntry) itemDebugEntry.status = 'Placeholder Creation';
      } else {
        logger.warn(
          `[DB][Order ${order.id}][Item ${orderItemId}] No AI personalizations and 'createPlaceholder' is false. No task will be created for this item.`
        );
        finalDataSource = 'Skipped_No_Data';
        if (itemDebugEntry) itemDebugEntry.status = 'Skipped - No Placeholder';
      }
    }

    // Task creation logic
    if (taskDetailsToCreate.length > 0) {
      for (let taskIndex = 0; taskIndex < taskDetailsToCreate.length; taskIndex++) {
        const detail = taskDetailsToCreate[taskIndex];
        // Use preserved text if options.preserveText is true and text exists for this taskIndex
        const textToUse =
          (options.preserveText && preservedTexts.get(taskIndex)) || detail.custom_text;

        // Data for the 'update' part of upsert - should only contain fields that can be updated
        const updatePayload = {
          custom_text: textToUse,
          color_1: detail.color_1,
          color_2: detail.color_2,
          quantity: detail.quantity,
          needs_review: detail.needs_review,
          review_reason: detail.review_reason,
          status: detail.status,
          annotation: detail.annotation,
          shorthandProductName: item.Product?.name
            ? item.Product.name.length > 100
              ? item.Product.name.substring(0, 97) + '...'
              : item.Product.name
            : 'Unknown',
          ship_by_date: order.ship_by_date,
          marketplace_order_number: order.shipstation_order_number,
        };

        const whereClause = {
          orderItemId_taskIndex: {
            orderItemId: orderItemId,
            taskIndex: taskIndex,
          },
        };

        // Data for the 'create' part of upsert - contains all required fields for a new task
        const createPayload = {
          id: crypto.randomUUID(),
          ...updatePayload,
          orderItemId: orderItemId,
          productId: item.productId,
          taskIndex: taskIndex,
          orderId: order.id,
        };

        try {
          const upsertedTask = await tx.printOrderTask.upsert({
            where: whereClause,
            update: updatePayload,
            create: createPayload,
          });
          tasksCreatedCount++;
          if (detail.needs_review) {
            itemsNeedReviewCount++;
          }
          if (itemDebugEntry) itemDebugEntry.createdTaskIds.push(upsertedTask.id);
          logger.info(
            {
              orderId: order.id,
              itemId: orderItemId,
              taskIndex,
              upsertedTaskId: upsertedTask.id,
              customText: upsertedTask.custom_text,
              color1: upsertedTask.color_1,
              status: finalDataSource,
            },
            `[DB-DEBUG][Order ${order.id}][Item ${orderItemId}][TaskIdx ${taskIndex}] POST-UPSERT: Upserted task ID ${upsertedTask.id}.`
          );
          if (itemDebugEntry) itemDebugEntry.status = 'Task ' + taskIndex + ' Upserted';
        } catch (dbError) {
          logger.error(
            {
              orderId: order.id,
              itemId: orderItemId,
              taskIndex,
              error: dbError,
            },
            `[DB-DEBUG][Order ${order.id}][Item ${orderItemId}][TaskIdx ${taskIndex}] UPSERT FAILED`
          );
          if (itemDebugEntry) {
            itemDebugEntry.status = 'Task ' + taskIndex + ' Upsert Failed';
            itemDebugEntry.error = dbError instanceof Error ? dbError.message : String(dbError);
          }
        }
      }
    } else {
      tasksSkippedCount++;
      logger.info(
        `[DB][Order ${order.id}][Item ${orderItemId}] No tasks generated. Incrementing tasksSkippedCount.`
      );
      if (itemDebugEntry) {
        itemDebugEntry.status = 'Skipped - No Tasks Generated';
      }
    }
  }

  logger.info(
    `[DB-DEBUG][Order ${order.id}] END OF createOrUpdateTasksInTransaction: tasksCreatedCount=${tasksCreatedCount}, itemsNeedReviewCount=${itemsNeedReviewCount}`
  );
  return { tasksCreatedCount, tasksSkippedCount, itemsNeedReviewCount };
}

/**
 * Syncs existing tasks to ShipStation
 */
export async function syncExistingTasksToShipstation(
  prisma: PrismaClient,
  orderId: number,
  options: ProcessingOptions,
  logger: pino.Logger
): Promise<ShipStationSyncResult> {
  logger.info(`[ShipStation Sync] Starting sync of existing tasks for order ${orderId}...`);
  const itemsToPatch: Record<string, Array<{ name: string; value: string | null }>> = {};
  const patchReasons: string[] = [];
  let updatedCount = 0;
  let failedCount = 0;

  try {
    const orderDetails = await prisma.order.findUnique({
      where: { id: orderId },
      select: {
        id: true,
        shipstation_order_id: true,
        shipstation_order_number: true,
        customer_notes: true,
        internal_notes: true,
        OrderItem: {
          select: {
            id: true,
            shipstationLineItemKey: true,
            Product: {
              select: {
                id: true,
                name: true,
              },
            },
            PrintOrderTask: {
              select: {
                id: true,
                custom_text: true,
                color_1: true,
                color_2: true,
                taskIndex: true,
              },
              orderBy: {
                taskIndex: 'asc',
              },
            },
          },
        },
      },
    });

    if (!orderDetails) {
      throw new Error(`Order with ID ${orderId} not found in database`);
    }

    if (!orderDetails.shipstation_order_id) {
      throw new Error(`Order ${orderId} missing ShipStation order ID`);
    }

    logger.info(
      `[ShipStation Sync] Found order ${orderId} with ShipStation order ID ${orderDetails.shipstation_order_id}`
    );

    const ssOrderResponse = await fetchOrdersWithRetry({
      orderId: Number(orderDetails.shipstation_order_id),
    });
    if (!ssOrderResponse || !ssOrderResponse.orders || ssOrderResponse.orders.length === 0) {
      throw new Error(
        `Failed to fetch order ${orderDetails.shipstation_order_id} details from ShipStation`
      );
    }

    const ssOrder = ssOrderResponse.orders[0];

    // Build packing list and item patches
    const allOrderPackingListLines: string[] = [];

    for (const item of orderDetails.OrderItem) {
      if (!item.shipstationLineItemKey) {
        logger.warn(
          `[ShipStation Sync][Item ${item.id}] Missing ShipStation line item key. Skipping.`
        );
        failedCount++;
        continue;
      }

      if (item.PrintOrderTask.length === 0) {
        logger.info(`[ShipStation Sync][Item ${item.id}] No print tasks. Skipping.`);
        continue;
      }

      // Build packing list lines
      item.PrintOrderTask.forEach((task, index) => {
        const detail = task.custom_text || 'N/A';
        const color1 = task.color_1;
        const color2 = task.color_2 ? ` / ${task.color_2}` : '';
        const taskDetailString = `${detail} (${color1 || 'N/A'}${color2})`;
        allOrderPackingListLines.push(`${index + 1}. ${taskDetailString}`);
      });

      const taskPersonalizations = item.PrintOrderTask.map(task => ({
        customText: task.custom_text,
        color1: task.color_1,
        color2: task.color_2,
        quantity: 1,
        needsReview: false,
        reviewReason: null,
        annotation: null,
      }));

      const detailsString = buildPersonalizedDetailsString(
        taskPersonalizations,
        item.shipstationLineItemKey,
        orderDetails.id,
        logger
      );

      if (taskPersonalizations.length > 0) {
        const ssOption = { name: 'Personalized Details', value: detailsString };
        itemsToPatch[item.shipstationLineItemKey] = [ssOption];
        patchReasons.push(`${item.shipstationLineItemKey} (PD - Sync)`);
        if (!options.dryRun) updatedCount++;
      }
    }

    const packingListHeader = `PACKING LIST(Order #${orderDetails.shipstation_order_number || 'N/A'}): `;
    const packingListString =
      allOrderPackingListLines.length > 0
        ? allOrderPackingListLines.join('\n')
        : 'No specific personalizations found in tasks.';

    const originalCustomerNotes =
      ssOrder.customerNotes || orderDetails.customer_notes || 'No customer notes provided.';

    const syncDetails = `Automated Task Sync(Existing) ${new Date().toISOString()} -> ${patchReasons.join(', ')}`;

    const finalAuditNoteForInternalNotes = `${packingListHeader}\n${packingListString}\n---\nOriginal Customer Notes:\n${originalCustomerNotes}\n---\n${syncDetails}`;

    if (options.dryRun) {
      if (Object.keys(itemsToPatch).length > 0) {
        logger.info(
          { orderId, itemsToPatch, internalNotesPreview: finalAuditNoteForInternalNotes },
          `[Dry Run][ShipStation Sync - Only] Would update ShipStation order ${orderDetails?.shipstation_order_id}`
        );
      } else {
        logger.info(
          { orderId },
          `[Dry Run][ShipStation Sync - Only] No items require updates for order ${orderId}.`
        );
      }
    } else if (Object.keys(itemsToPatch).length > 0 && ssOrder) {
      try {
        await updateOrderItemsOptionsBatch(
          ssOrder,
          itemsToPatch,
          finalAuditNoteForInternalNotes,
          null,
          null
        );
        logger.info(
          `[ShipStation Sync] Successfully updated items in ShipStation for order ${orderId}: ${patchReasons.join(', ')}`
        );
      } catch (batchErr) {
        logger.error(`[ShipStation Sync][Order ${orderId}] Batch update error`, batchErr);
        failedCount += Object.keys(itemsToPatch).length;
        updatedCount -= Object.keys(itemsToPatch).length;
        if (updatedCount < 0) updatedCount = 0;
      }
    } else {
      logger.info(
        `[ShipStation Sync] No item options to update in ShipStation for order ${orderId}.`
      );
    }

    logger.info(
      `[ShipStation Sync] Completed sync for order ${orderId}. Items updated: ${updatedCount}, Items failed: ${failedCount}`
    );
    return { updatedCount, failedCount };
  } catch (error) {
    logger.error(
      { err: error, orderId },
      `[ShipStation Sync] Failed to sync order ${orderId}: ${error instanceof Error ? error.message : String(error)}`
    );
    failedCount = Math.max(failedCount, 1);
  }
  return { updatedCount, failedCount };
}
