import { Prisma, PrintOrderTask_status } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// Helper function to check if a string is a valid PrintOrderTask_status
function isValidPrintTaskStatus(status: unknown): status is PrintOrderTask_status {
  return (
    typeof status === 'string' &&
    Object.values(PrintOrderTask_status).includes(status as PrintOrderTask_status)
  );
}

export async function PATCH(request: NextRequest, { params }: { params: { taskId: string } }) {
  // --- Get Session ---
  const session = await getServerSession(authOptions);
  if (!session || !session.user) {
    console.error('[API Status PATCH] Unauthorized: No session found.');
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }
  // console.log(`[API Status PATCH] Authorized user: ${session.user.email}`); // Optional logging
  // --- End Get Session ---

  try {
    const { taskId } = params;
    if (!taskId || typeof taskId !== 'string') {
      // Basic validation for taskId string
      return NextResponse.json({ error: 'Invalid Task ID provided' }, { status: 400 });
    }

    let body;
    try {
      body = await request.json();
    } catch (error) {
      console.error(`Invalid JSON body for task ${taskId}:`, error);
      return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    const { status } = body;
    if (!status || !isValidPrintTaskStatus(status)) {
      return NextResponse.json(
        {
          error: `Invalid or missing status. Must be one of: ${Object.values(PrintOrderTask_status).join(', ')}`,
        },
        { status: 400 }
      );
    }

    try {
      const updatedTask = await prisma.printOrderTask.update({
        where: { id: taskId },
        data: { status, updated_at: new Date() },
      });
      return NextResponse.json(updatedTask);
    } catch (dbError) {
      console.error(`Database error updating task ${taskId} status:`, dbError);
      if (dbError instanceof Prisma.PrismaClientKnownRequestError) {
        if (dbError.code === 'P2025') {
          return NextResponse.json({ error: 'Task not found' }, { status: 404 });
        }
      }
      return NextResponse.json({ error: 'Failed to update task status' }, { status: 500 });
    }
  } catch (error) {
    console.error('[API Status PATCH] General error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// Optional: Add OPTIONS method for CORS preflight requests if needed,
// although typically not required for same-origin requests in Next.js App Router.
// export async function OPTIONS() {
//   return new NextResponse(null, {
//     status: 204,
//     headers: {
//       'Access-Control-Allow-Origin': '*', // Adjust as needed
//       'Access-Control-Allow-Methods': 'PATCH, OPTIONS',
//       'Access-Control-Allow-Headers': 'Content-Type, Authorization',
//     },
//   });
// }
