#!/usr/bin/env tsx
import { promises as fs } from 'fs';
import path from 'path';

import { PrintOrderTask_stl_render_state, PrismaClient } from '@prisma/client';
import { Command } from 'commander';
import slug from 'slug';

import { getLogger } from '@/lib/shared/logging';
import 'dotenv/config';

const logger = getLogger('check-missing-stl-files');
const prisma = new PrismaClient();

interface MissingFileReport {
  taskId: string;
  customText: string | null;
  productSku: string;
  expectedPath: string;
  stlPath: string | null;
  renderState: PrintOrderTask_stl_render_state;
  exists: boolean;
  azureExists?: boolean;
}

// Helper functions from STL worker
function getProductFolder(sku: string): string {
  if (sku === 'PER-KEY3D-STY3-Y3D' || sku.startsWith('PER-KEY3D')) return 'dual-colours';
  if (sku === 'Y3D-NKC-002' || sku.startsWith('Y3D-NKC-002-')) return 'bubble-style';
  if (sku === 'Y3D-NKC-001' || sku.startsWith('Y3D-NKC-001-')) return 'signature-style';
  if (sku === 'N9-93VU-76VK') return 'new3-tag';
  if (sku === 'Y3D-REGKEY-STL1') return 'reg-keys';
  if (sku.startsWith('PER-2PER-')) return 'cable-clip';
  return 'other';
}

function getAlphaFolder(text: string): string {
  const firstChar = text.charAt(0).toUpperCase();
  return /[A-Z]/.test(firstChar) ? firstChar : 'OTHER';
}

function constructExpectedPath(
  customText: string | null,
  productSku: string,
  taskId: string
): string {
  const productFolder = getProductFolder(productSku);

  // Use first line of customText for alpha folder and filename
  const mainText =
    customText
      ?.split(/\r?\n|\\|\//)
      .map(t => t.trim())
      .filter(Boolean)[0] || `Tag${taskId}`;

  const alphaFolder = getAlphaFolder(slug(mainText));

  // Filename: safe slug of mainText, add _UC if all uppercase
  const safeName = slug(mainText);
  const isAllUpper = safeName === safeName.toUpperCase() && /[A-Z]/.test(safeName);
  const baseOutputFilename = `${safeName}${isAllUpper ? '_UC' : ''}.stl`;

  return path.join(productFolder, alphaFolder, baseOutputFilename);
}

async function checkFileExists(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

async function checkMissingSTLFiles(options: {
  outputDir?: string;
  azureDir?: string;
  includeCompleted?: boolean;
  dryRun?: boolean;
  fixMissing?: boolean;
}): Promise<void> {
  const outputBaseDir = path.resolve(options.outputDir || './output_stl');
  const azureSharedDir = options.azureDir || '/mnt/azure-fileshare/shared/stl-files';

  logger.info('Starting STL file check for awaiting_shipment orders...');
  logger.info(`Local output directory: ${outputBaseDir}`);
  logger.info(`Azure shared directory: ${azureSharedDir}`);

  // Get print tasks for orders that are still awaiting shipping
  const whereClause = options.includeCompleted
    ? {
        stl_render_state: {
          in: [PrintOrderTask_stl_render_state.completed, PrintOrderTask_stl_render_state.pending],
        },
        OrderItem: {
          Order: {
            order_status: 'awaiting_shipment',
          },
        },
      }
    : {
        stl_render_state: PrintOrderTask_stl_render_state.completed,
        OrderItem: {
          Order: {
            order_status: 'awaiting_shipment',
          },
        },
      };

  const tasks = await prisma.printOrderTask.findMany({
    where: whereClause,
    include: {
      OrderItem: {
        include: {
          Product: true,
          Order: true,
        },
      },
    },
    orderBy: {
      updated_at: 'desc',
    },
  });

  logger.info(`Found ${tasks.length} tasks to check`);

  const missingFiles: MissingFileReport[] = [];
  const foundFiles: MissingFileReport[] = [];

  for (const task of tasks) {
    const productSku = task.OrderItem?.Product?.sku || 'UNKNOWN';
    const expectedPath = constructExpectedPath(task.custom_text, productSku, task.id);
    const localFilePath = path.join(outputBaseDir, expectedPath);
    const azureFilePath = path.join(azureSharedDir, expectedPath);

    const localExists = await checkFileExists(localFilePath);
    const azureExists = await checkFileExists(azureFilePath);

    const report: MissingFileReport = {
      taskId: task.id,
      customText: task.custom_text,
      productSku,
      expectedPath,
      stlPath: task.stl_path,
      renderState: task.stl_render_state,
      exists: localExists,
      azureExists,
    };

    if (!localExists || !azureExists) {
      missingFiles.push(report);
    } else {
      foundFiles.push(report);
    }
  }

  // Report results
  logger.info(`\n=== STL FILE CHECK RESULTS ===`);
  logger.info(`Total tasks checked: ${tasks.length}`);
  logger.info(`Files found (local): ${foundFiles.length}`);
  logger.info(`Files missing (local): ${missingFiles.filter(f => !f.exists).length}`);
  logger.info(`Files missing (azure): ${missingFiles.filter(f => !f.azureExists).length}`);

  if (missingFiles.length > 0) {
    logger.warn(`\n=== MISSING FILES (${missingFiles.length}) ===`);
    for (const missing of missingFiles) {
      const localStatus = missing.exists ? '✅' : '❌';
      const azureStatus = missing.azureExists ? '✅' : '❌';

      logger.warn(`Task: ${missing.taskId}`);
      logger.warn(`  Text: "${missing.customText}"`);
      logger.warn(`  SKU: ${missing.productSku}`);
      logger.warn(`  Expected: ${missing.expectedPath}`);
      logger.warn(`  DB Path: ${missing.stlPath || 'NULL'}`);
      logger.warn(`  State: ${missing.renderState}`);
      logger.warn(`  Local: ${localStatus} | Azure: ${azureStatus}`);
      logger.warn('---');
    }

    if (options.fixMissing && !options.dryRun) {
      logger.info('\n=== FIXING MISSING FILES ===');
      const tasksToReset = missingFiles.filter(
        f => !f.exists && f.renderState === PrintOrderTask_stl_render_state.completed
      );

      if (tasksToReset.length > 0) {
        logger.info(
          `Resetting ${tasksToReset.length} completed tasks with missing files to pending...`
        );

        const taskIds = tasksToReset.map(t => t.taskId);
        const result = await prisma.printOrderTask.updateMany({
          where: {
            id: { in: taskIds },
          },
          data: {
            stl_render_state: PrintOrderTask_stl_render_state.pending,
            stl_path: null,
            annotation: 'Reset to pending - STL file missing from expected location',
            updated_at: new Date(),
          },
        });

        logger.info(`Reset ${result.count} tasks to pending state`);
      }
    }
  } else {
    logger.info('✅ All expected STL files found!');
  }

  logger.info('\n=== CHECK COMPLETE ===');
}

async function main(): Promise<void> {
  const program = new Command();

  program
    .name('check-missing-stl-files')
    .description('Check for missing STL files for print tasks')
    .option('--output-dir <path>', 'Local STL output directory', './output_stl')
    .option('--azure-dir <path>', 'Azure shared directory', '/mnt/azure-fileshare/shared/stl-files')
    .option('--include-completed', 'Include completed tasks in check', false)
    .option('--fix-missing', 'Reset missing completed tasks to pending', false)
    .option('--dry-run', 'Show what would be done without making changes', false);

  program.parse();
  const options = program.opts();

  try {
    await checkMissingSTLFiles(options);
  } catch (error) {
    logger.error('Error during STL file check:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run main function if this is the entry point
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
